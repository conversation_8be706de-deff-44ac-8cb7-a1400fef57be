import requests
import json
import time
from typing import Dict, Any, Optional


class SingleProductTester:
    """单商品发布测试工具"""
    
    def __init__(self, cookie: str = None):
        # 默认Cookie（需要用户更新）
        default_cookie = 'MYJ_MKTG_fapsc5t4tc=JTdCJTdE; MYJ_fapsc5t4tc=****************************************************************************************************************************************************************************************************************************************************************************************; _clck=e8utqr%7C2%7Cfx3%7C0%7C1916; _dxm_ad_client_id=A598CC49F8B80613EF428FD74B7439D2F; Hm_lvt_f8001a3f3d9bf5923f780580eb550c0b=**********,**********,**********,**********; HMACCOUNT=C15F99DB42A7BA7A; dxm_i=MjA4MzA1OSFhVDB5TURnek1EVTUhYzY4N2EzMWZmYWI1YTJhNTdhZjgwNWQ3MjdhYmY0NTk; dxm_t=********************************************************************************; dxm_c=ZTZCTTluZjghWXoxbE5rSk5PVzVtT0EhOTc1NjI5YjA2MTI1NjAxMGNhNTU3ZTcxNzExODI5YzU; dxm_w=MjZiM2JjZjIyMDhmMTQxMmJhMmRiMjVhNjNmYTI2MWEhZHoweU5tSXpZbU5tTWpJd09HWXhOREV5WW1FeVpHSXlOV0UyTTJaaE1qWXhZUSExM2JlYTk1NzRmNGY4N2IxNmNkNTk1YjJjMzdhNTgzMQ; dxm_s=1WgJuVor95ml7WCBI6mbhUQHhU5inFaADopIS6YaaIk; MYJ_fapsc5t4tc=****************************************************************************************************************************************************************************************************************************************************************************************; Hm_lpvt_f8001a3f3d9bf5923f780580eb550c0b=**********; JSESSIONID=710335BA095F715F7E5660C34A73701D'
        
        self.base_headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'content-type': 'application/x-www-form-urlencoded',
            'priority': 'u=1, i',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'referrer': 'https://www.dianxiaomi.com/web/popTemu/pageList/offline?dxmOfflineState=publishFail',
            'referrerPolicy': 'strict-origin-when-cross-origin',
            'Cookie': cookie if cookie else default_cookie,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        self.step_delay = 2  # 每步骤间隔时间（秒）
    
    def print_separator(self, title: str):
        """打印分隔线"""
        print(f"\n{'='*80}")
        print(f"  {title}")
        print(f"{'='*80}")
    
    def print_request_info(self, step: str, url: str, data: Dict[str, str]):
        """打印请求信息"""
        print(f"\n📤 {step} - 请求信息:")
        print(f"URL: {url}")
        print(f"Method: POST")
        print(f"Headers:")
        for key, value in self.base_headers.items():
            if key == 'Cookie':
                print(f"  {key}: {value[:100]}...（已截断）")
            else:
                print(f"  {key}: {value}")
        print(f"Data:")
        for key, value in data.items():
            print(f"  {key}: {value}")
    
    def print_response_info(self, step: str, response: requests.Response):
        """打印响应信息"""
        print(f"\n📥 {step} - 响应信息:")
        print(f"状态码: {response.status_code}")
        print(f"响应头:")
        for key, value in response.headers.items():
            print(f"  {key}: {value}")
        
        print(f"响应体:")
        try:
            json_data = response.json()
            print(json.dumps(json_data, indent=2, ensure_ascii=False))
        except:
            print(response.text)
    
    def make_detailed_request(self, step: str, url: str, data: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """发送详细的HTTP请求"""
        self.print_request_info(step, url, data)
        
        try:
            response = requests.post(url, headers=self.base_headers, data=data)
            self.print_response_info(step, response)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"\n❌ {step} 请求失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"\n❌ {step} 请求异常: {e}")
            return None
    
    def test_step1_price_detection(self, product_id: str) -> bool:
        """测试步骤1: 价格保护检测"""
        self.print_separator("步骤1: 价格保护检测")
        
        url = "https://www.dianxiaomi.com/api/priceDetection/batchProtectPriceDetection.json"
        data = {
            'dxmState': 'offline',
            'platform': 'popTemu',
            'ids': product_id
        }
        
        result = self.make_detailed_request("步骤1", url, data)

        if result and result.get('code') == 0:
            print(f"\n✅ 步骤1 成功")
            return True
        else:
            print(f"\n❌ 步骤1 失败")
            if result:
                error_code = result.get('code')
                error_msg = result.get('msg')
                print(f"错误代码: {error_code}")
                print(f"错误信息: {error_msg}")

                if error_code == 2001:
                    print("💡 分析: Cookie验证失败，可能是Cookie过期或无效")
                elif error_code == 403:
                    print("💡 分析: 权限不足，请检查账号权限")
                elif error_code == 404:
                    print("💡 分析: 商品ID不存在或已删除")
                elif error_code == 0:
                    print("💡 分析: 请求成功，但可能需要检查具体业务逻辑")
            return False
    
    def test_step2_banned_word_check(self, product_id: str) -> bool:
        """测试步骤2: 违禁词检查"""
        self.print_separator("步骤2: 违禁词检查")
        
        url = "https://www.dianxiaomi.com/api/bannedWord/batchCheckForBannedWord.json"
        data = {
            'productIds': product_id,
            'platform': 'popTemu'
        }
        
        result = self.make_detailed_request("步骤2", url, data)

        if result and result.get('code') == 0:
            print(f"\n✅ 步骤2 成功")
            return True
        else:
            print(f"\n❌ 步骤2 失败")
            return False
    
    def test_step3_product_info_check(self, product_id: str) -> Optional[str]:
        """测试步骤3: 商品信息检查"""
        self.print_separator("步骤3: 商品信息检查")
        
        url = "https://www.dianxiaomi.com/api/productCheck/batchCheckProductInfo.json"
        data = {
            'ids': product_id,
            'platform': 'popTemu'
        }
        
        result = self.make_detailed_request("步骤3", url, data)

        if result and result.get('code') == 0:
            uuid = result.get('data', {}).get('uuid')
            if uuid:
                print(f"\n✅ 步骤3 成功，获得UUID: {uuid}")
                return uuid
            else:
                print(f"\n✅ 步骤3 成功，但未获取到UUID（可能不需要）")
                return "no_uuid_needed"  # 返回特殊值表示成功但无UUID
        else:
            print(f"\n❌ 步骤3 失败")
            return None
    
    def test_step4_check_process(self, uuid: str) -> bool:
        """测试步骤4: 检查进度"""
        self.print_separator("步骤4: 检查进度")
        
        url = "https://www.dianxiaomi.com/api/checkProcess.json"
        data = {'uuid': uuid}
        
        result = self.make_detailed_request("步骤4", url, data)

        if result and result.get('code') == 0:
            status = result.get('data', {}).get('status')
            print(f"\n检查状态: {status}")

            if status == 'completed':
                print(f"✅ 步骤4 成功")
                return True
            elif status == 'failed':
                print(f"❌ 步骤4 失败")
                return False
            else:
                print(f"⏳ 步骤4 进行中")
                return True  # 对于测试，我们认为进行中也是成功的
        else:
            print(f"\n❌ 步骤4 失败")
            return False
    
    def test_step5_batch_publish(self, product_id: str) -> bool:
        """测试步骤5: 批量发布"""
        self.print_separator("步骤5: 批量发布")
        
        url = "https://www.dianxiaomi.com/api/popTemuProduct/batchPublish.json"
        data = {
            'ids': product_id,
            'shopId': '-1'
        }
        
        result = self.make_detailed_request("步骤5", url, data)

        if result and result.get('code') == 0:
            print(f"\n✅ 步骤5 成功")
            return True
        else:
            print(f"\n❌ 步骤5 失败")
            return False
    
    def test_complete_flow(self, product_id: str, stop_on_error: bool = True):
        """测试完整发布流程"""
        print(f"\n🚀 开始测试商品发布流程")
        print(f"商品ID: {product_id}")
        print(f"停止策略: {'遇到错误停止' if stop_on_error else '继续执行所有步骤'}")
        
        steps_results = []
        
        # 步骤1: 价格保护检测
        step1_success = self.test_step1_price_detection(product_id)
        steps_results.append(("步骤1: 价格保护检测", step1_success))
        
        if not step1_success and stop_on_error:
            self.print_final_summary(steps_results)
            return
        
        time.sleep(self.step_delay)
        
        # 步骤2: 违禁词检查
        step2_success = self.test_step2_banned_word_check(product_id)
        steps_results.append(("步骤2: 违禁词检查", step2_success))
        
        if not step2_success and stop_on_error:
            self.print_final_summary(steps_results)
            return
        
        time.sleep(self.step_delay)
        
        # 步骤3: 商品信息检查
        uuid = self.test_step3_product_info_check(product_id)
        step3_success = uuid is not None
        steps_results.append(("步骤3: 商品信息检查", step3_success))
        
        if not step3_success and stop_on_error:
            self.print_final_summary(steps_results)
            return
        
        if uuid:
            time.sleep(self.step_delay)
            
            # 步骤4: 检查进度
            step4_success = self.test_step4_check_process(uuid)
            steps_results.append(("步骤4: 检查进度", step4_success))
            
            if not step4_success and stop_on_error:
                self.print_final_summary(steps_results)
                return
        
        time.sleep(self.step_delay)
        
        # 步骤5: 批量发布
        step5_success = self.test_step5_batch_publish(product_id)
        steps_results.append(("步骤5: 批量发布", step5_success))
        
        self.print_final_summary(steps_results)
    
    def print_final_summary(self, steps_results):
        """打印最终总结"""
        self.print_separator("测试结果总结")
        
        success_count = 0
        for step_name, success in steps_results:
            status = "✅ 成功" if success else "❌ 失败"
            print(f"{step_name}: {status}")
            if success:
                success_count += 1
        
        print(f"\n总体结果: {success_count}/{len(steps_results)} 步骤成功")
        
        if success_count == len(steps_results):
            print("🎉 所有步骤都成功！可以进行批量发布")
        else:
            print("⚠️  存在失败步骤，请检查上述详细信息")


def main():
    """主函数"""
    print("🧪 单商品发布测试工具")
    print("="*50)
    
    # 获取测试商品ID
    product_id = input("请输入要测试的商品ID (默认: 138220088856533368): ").strip()
    if not product_id:
        product_id = "138220088856533368"
    
    # 询问是否更新Cookie
    update_cookie = input("\n是否需要更新Cookie？(y/n): ").strip().lower()
    
    if update_cookie == 'y':
        print("\n请粘贴完整的Cookie字符串:")
        new_cookie = input().strip()
        if new_cookie:
            tester = SingleProductTester(cookie=new_cookie)
            print("✅ Cookie已更新")
        else:
            print("❌ Cookie为空，使用默认Cookie")
            tester = SingleProductTester()
    else:
        tester = SingleProductTester()
    
    # 询问测试模式
    print("\n选择测试模式:")
    print("1. 遇到错误立即停止（推荐）")
    print("2. 执行所有步骤（无论是否出错）")
    
    mode = input("请选择 (1/2): ").strip()
    stop_on_error = mode != '2'
    
    # 开始测试
    tester.test_complete_flow(product_id, stop_on_error)


if __name__ == "__main__":
    main()
