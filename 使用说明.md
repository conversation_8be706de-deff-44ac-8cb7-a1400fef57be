# 店小秘商品发布工具使用说明

## 功能概述
这个工具可以自动化执行店小秘商品发布流程，支持单个测试和批量发布。

## 使用步骤

### 1. 准备工作
- 确保已登录店小秘网站
- 准备好商品ID列表文件（`商品ID列表.txt`）

### 2. 获取Cookie
1. 打开浏览器，登录店小秘网站
2. 按F12打开开发者工具
3. 切换到Network标签页
4. 在网站上执行任意操作（如刷新页面）
5. 找到任意请求，复制完整的Cookie字符串

### 3. 运行工具
```bash
python 商品发布工具.py
```

### 4. 操作选择
- **更新Cookie**: 粘贴从浏览器复制的Cookie
- **测试模式**: 先测试单个商品，验证Cookie有效性
- **批量发布**: 确认无误后开始批量发布

## 发布流程说明

工具会按以下顺序执行每个商品的发布：

1. **价格保护检测** - 检查商品价格设置
2. **违禁词检查** - 检查商品标题和描述中的违禁词
3. **商品信息检查** - 验证商品信息完整性
4. **检查进度查询** - 等待检查完成
5. **批量发布** - 正式发布商品

## 注意事项

⚠️ **重要提醒**：
- 请确保Cookie是最新的，过期Cookie会导致验证失败
- 建议先用测试模式验证配置正确性
- 批量发布时请耐心等待，避免中断程序
- 发布过程中会有延时，避免请求过快被限制

## 错误处理

常见错误及解决方案：

- **验证失败(code: 2001)**: Cookie过期，需要更新
- **网络超时**: 检查网络连接，稍后重试
- **商品信息错误**: 检查商品ID是否有效

## 输出文件

- `商品ID列表.txt`: 输入的商品ID列表
- 控制台输出: 详细的发布进度和结果统计
